/**
 * 系统管理员认证服务
 * 专门处理系统管理员（超管、管理、员工）的登录认证和用户信息管理
 */

// 导入API模块
import authApi from '../api/auth.js'
import CryptoJS from 'crypto-js'

// 用户类型映射 - 映射数字类型到字符串
// 根据数据库表结构：1:超级管理员 2:管理员 3:员工
export const ADMIN_USER_TYPES = {
  1: 'admin',     // 超管
  2: 'manager',   // 管理
  3: 'employee',  // 员工
  'ip_user': 'ip_user',  // IP用户
  'user': 'user'  // 普通用户（兼容）
}

// 反向映射
export const ADMIN_USER_TYPE_NAMES = {
  1: '超管',
  2: '管理',
  3: '员工'
}

// 存储键名 - 与微信用户完全分离
const ADMIN_STORAGE_KEYS = {
  LOGIN_INFO: 'adminLoginInfo',
  USER_PERMISSIONS: 'adminUserPermissions'
}

/**
 * 系统管理员认证服务类
 */
class AdminAuthService {
  /**
   * 系统管理员登录认证
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise<Object>} 登录结果
   */
  async authenticate (username, password) {
    try {
      // 对密码进行MD5加密
      const hashedPassword = CryptoJS.MD5(password).toString()

      const response = await authApi.login({ username, password: hashedPassword })

      if (response && response.success && response.data) {
        const userInfo = response.data.userInfo
        const permissions = response.data.permissions || []

        // 映射用户类型
        const userType = ADMIN_USER_TYPES[userInfo.UserType] || 'employee'

        const loginInfo = {
          username: userInfo.Username || username,
          userId: userInfo.UserId || userInfo.Username || username,
          nickName: userInfo.NickName,
          userType: userType,
          userTypeCode: userInfo.UserType,
          accessToken: response.data.accessToken,
          permissions: permissions,
          loginTime: new Date().getTime()
        }

        // 保存登录信息
        this.saveLoginInfo(loginInfo)

        return {
          success: true,
          message: '登录成功',
          user: loginInfo
        }
      } else {
        const errorMessage = (response && response.msg) || '登录失败，请检查用户名和密码'
        return {
          success: false,
          message: errorMessage
        }
      }

    } catch (error) {
      console.error('Admin authentication error:', error)
      const errorMessage = error.message || '登录失败，请重试'
      return {
        success: false,
        message: errorMessage
      }
    }
  }

  /**
   * Save login information to storage
   * @param {Object} loginInfo
   */
  saveLoginInfo (loginInfo) {
    try {
      uni.setStorageSync(ADMIN_STORAGE_KEYS.LOGIN_INFO, loginInfo)

      // Save user permissions
      uni.setStorageSync(ADMIN_STORAGE_KEYS.USER_PERMISSIONS, loginInfo.permissions)

    } catch (error) {
      console.error('Error saving login info:', error)
    }
  }

  /**
   * Get current login information
   * @returns {Object|null}
   */
  getLoginInfo () {
    try {
      return uni.getStorageSync(ADMIN_STORAGE_KEYS.LOGIN_INFO) || null
    } catch (error) {
      console.error('Error getting login info:', error)
      return null
    }
  }

  /**
   * Check if user is logged in
   * @returns {boolean}
   */
  isLoggedIn () {
    const loginInfo = this.getLoginInfo()
    return !!(loginInfo && loginInfo.username)
  }

  /**
   * Check if current user has specific permission
   * @param {string} permission
   * @returns {boolean}
   */
  hasPermission (permission) {
    try {
      const permissions = uni.getStorageSync(ADMIN_STORAGE_KEYS.USER_PERMISSIONS) || []
      return permissions.includes('*') || permissions.includes(permission)
    } catch (error) {
      console.error('Error checking permission:', error)
      return false
    }
  }

  /**
   * Get current user type
   * @returns {string|null}
   */
  getUserType () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? loginInfo.userType : null
  }

  /**
   * Get current username
   * @returns {string|null}
   */
  getUsername () {
    const loginInfo = this.getLoginInfo()
    return loginInfo?.username ?? null
  }

  /**
   * Get current user display name
   * @returns {string|null}
   */
  getUserName () {
    const loginInfo = this.getLoginInfo()
    return loginInfo?.name ?? null
  }

  /**
   * 获取当前管理员用户ID
   * @returns {string|null}
   */
  getUserId () {
    const loginInfo = this.getLoginInfo()
    return loginInfo?.userId ?? null
  }

  /**
   * 获取当前管理员显示名称
   * @returns {string|null}
   */
  getUserDisplayName () {
    const loginInfo = this.getLoginInfo()
    return loginInfo?.nickName ?? loginInfo?.username ?? null
  }

  /**
   * Logout current user
   */
  async logout () {
    try {
      // 调用服务端登出API
      try {
        await authApi.logout()
      } catch (apiError) {
        console.warn('Server logout failed:', apiError)
        // 即使服务端登出失败，也继续清理本地数据
      }

      // 清理本地存储
      uni.removeStorageSync(ADMIN_STORAGE_KEYS.LOGIN_INFO)
      uni.removeStorageSync(ADMIN_STORAGE_KEYS.USER_PERMISSIONS)

      // Hide tab bar
      uni.hideTabBar()

      return true
    } catch (error) {
      console.error('Error during logout:', error)
      return false
    }
  }

  /**
   * 获取当前用户信息（从服务器）
   * @returns {Promise<Object|null>} 用户信息或null
   */
  async fetchCurrentUserInfo () {
    try {
      const response = await authApi.getUserInfo()

      if (response && response.success && response.data) {
        const userInfo = response.data

        // 更新本地存储的用户信息
        const loginInfo = this.getLoginInfo()
        if (loginInfo) {
          loginInfo.userId = userInfo.UserId
          loginInfo.username = userInfo.Username
          loginInfo.nickName = userInfo.NickName
          loginInfo.name = userInfo.NickName || userInfo.Username
          loginInfo.userType = ADMIN_USER_TYPES[userInfo.UserType] || 'employee'
          loginInfo.userTypeCode = userInfo.UserType

          // 添加更多用户信息字段
          loginInfo.avatar = userInfo.avatar || '/static/images/avatar-placeholder.png'
          loginInfo.email = userInfo.email || ''
          loginInfo.phone = userInfo.phone || userInfo.mobile || ''
          loginInfo.realName = userInfo.realName || userInfo.nickName || userInfo.username
          loginInfo.department = userInfo.department || ''
          loginInfo.position = userInfo.position || ''
          loginInfo.lastLoginTime = userInfo.lastLoginTime || ''

          this.saveLoginInfo(loginInfo)
        }

        return userInfo
      }

      return null
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      return null
    }
  }

  /**
   * 获取完整的用户显示信息（结合本地存储和服务器数据）
   * @param {boolean} forceRefresh - 是否强制从服务器刷新
   * @returns {Promise<Object>} 用户显示信息
   */
  async getCompleteUserInfo (forceRefresh = false) {
    if (!this.isLoggedIn()) {
      return {
        name: '未登录',
        username: '',
        userType: '',
        userTypeText: '游客',
        avatar: '/static/images/avatar-placeholder.png',
        email: '',
        phone: '',
        realName: '',
        department: '',
        position: '',
        lastLoginTime: ''
      }
    }

    let loginInfo = this.getLoginInfo()

    // 如果强制刷新或者本地信息不完整，从服务器获取最新信息
    if (forceRefresh || !loginInfo.avatar || !loginInfo.email) {
      const serverUserInfo = await this.fetchCurrentUserInfo()
      if (serverUserInfo) {
        loginInfo = this.getLoginInfo() // 重新获取更新后的信息
      }
    }

    const typeMap = {
      'employee': '员工',
      'manager': '管理',
      'admin': '超管'
    }

    return {
      userId: loginInfo.userId || '',
      name: loginInfo.name || loginInfo.nickName || loginInfo.username || '用户',
      username: loginInfo.username || '',
      nickName: loginInfo.nickName || '',
      realName: loginInfo.realName || loginInfo.nickName || loginInfo.username || '',
      userType: loginInfo.userType || '',
      userTypeText: typeMap[loginInfo.userType] || '用户',
      userTypeCode: loginInfo.userTypeCode || '',
      avatar: loginInfo.avatar || '/static/images/avatar-placeholder.png',
      email: loginInfo.email || '',
      phone: loginInfo.phone || '',
      department: loginInfo.department || '',
      position: loginInfo.position || '',
      lastLoginTime: loginInfo.lastLoginTime || '',
      loginTime: loginInfo.loginTime || '',
      permissions: loginInfo.permissions || []
    }
  }

  /**
   * Check if login session is valid (not expired)
   * @returns {boolean}
   */
  isSessionValid () {
    const loginInfo = this.getLoginInfo()
    if (!loginInfo || !loginInfo.loginTime) {
      return false
    }

    // Session expires after 7 days
    const sessionDuration = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
    const currentTime = new Date().getTime()

    return (currentTime - loginInfo.loginTime) < sessionDuration
  }

  /**
   * Refresh login session
   */
  refreshSession () {
    const loginInfo = this.getLoginInfo()
    if (loginInfo) {
      loginInfo.loginTime = new Date().getTime()
      this.saveLoginInfo(loginInfo)
    }
  }

  /**
   * Redirect to login page
   */
  redirectToLogin () {
    uni.reLaunch({
      url: '/pages/login/index'
    })
  }

  /**
   * Redirect to main page based on user type
   */
  redirectToMain () {
    const userType = this.getUserType()

    uni.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    })

    console.log(`登录成功! 用户类型: ${userType}`)
    console.log('正在跳转到仪表板: /pages/index/index')

    // Use setTimeout to allow toast to show before redirect
    setTimeout(() => {
      // 所有用户都跳转到仪表板页面（TabBar页面）
      // 使用 reLaunch 确保清除登录页面历史，并且支持跳转到TabBar页面
      uni.reLaunch({
        url: '/pages/index/index',
        success: () => {
          console.log('跳转成功')
          // 显示TabBar（如果当前页面支持）
          try {
            uni.showTabBar({
              success: () => {
                console.log('TabBar显示成功')
              },
              fail: (err) => {
                console.log('TabBar显示失败:', err)
              }
            })
          } catch (e) {
            console.log('TabBar操作异常:', e)
          }
        },
        fail: (err) => {
          console.error('页面跳转失败:', err)
          // 如果 reLaunch 失败，尝试使用 switchTab
          uni.switchTab({
            url: '/pages/index/index',
            success: () => {
              console.log('switchTab跳转成功')
            },
            fail: (err2) => {
              console.error('switchTab也失败了:', err2)
              // 最后的备选方案：使用 navigateTo
              uni.navigateTo({
                url: '/pages/index/index'
              })
            }
          })
        }
      })
    }, 1500)
  }

  /**
   * 获取管理员用户类型显示文本
   * @returns {string}
   */
  getUserTypeText () {
    const loginInfo = this.getLoginInfo()
    if (!loginInfo) return '未登录'

    return ADMIN_USER_TYPE_NAMES[loginInfo.userTypeCode] || '未知'
  }

  /**
   * Utility function to create delay
   * @param {number} ms
   * @returns {Promise}
   */
  delay (ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 创建单例实例
const adminAuthService = new AdminAuthService()

// 导出服务实例和工具函数
export default adminAuthService
export { AdminAuthService, ADMIN_STORAGE_KEYS }

// 导出常用函数以便使用
export const getAdminLoginInfo = () => adminAuthService.getLoginInfo()
export const isAdminLoggedIn = () => adminAuthService.isLoggedIn()
export const getAdminUserType = () => adminAuthService.getUserType()
export const getAdminUsername = () => adminAuthService.getUsername()
export const getAdminUserId = () => adminAuthService.getUserId()
export const getAdminUserName = () => adminAuthService.getUserName()
