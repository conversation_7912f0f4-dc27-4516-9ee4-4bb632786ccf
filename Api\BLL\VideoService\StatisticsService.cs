using BLL.Common;
using Common.Autofac;
using Common.Exceptions;
using Common.JWT;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;

namespace BLL.VideoService
{
    /// <summary>
    /// 统计服务（兼容性服务）
    /// 基于实时计算提供统计数据，将请求转发到实时统计服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class StatisticsService : BasePermissionService
    {
        private readonly UserBatchRecordService _userBatchRecordService;

        public StatisticsService(
            UserBatchRecordService userBatchRecordService,
            UserDAL userDAL,
            SysUserDAL sysUserDAL) : base(userDAL, sysUserDAL)
        {
            _userBatchRecordService = userBatchRecordService;
        }

        /// <summary>
        /// 获取统计汇总数据（重定向到实时统计服务，带权限验证）
        /// </summary>
        /// <param name="summaryDto">汇总查询DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>汇总统计数据</returns>
        public async Task<StatisticsSummaryDto> GetStatisticsSummaryAsync(StatisticsSummaryQueryDto summaryDto, CurrentUserInfoDto currentUserInfo)
        {
            // 权限验证：转换CurrentUserInfoDto为UserInfo进行验证
            var userInfo = new UserInfo
            {
                UserId = currentUserInfo.UserId,
                UserName = currentUserInfo.UserName,
                UserType = currentUserInfo.UserType
            };
            ValidateUserTypePermission(userInfo, [1, 2, 3], "权限不足，无法查看统计汇总数据");

            // 重定向到实时统计服务
            var start = summaryDto.StartDate ?? DateTime.Today.AddDays(-30);
            var end = summaryDto.EndDate ?? DateTime.Today;

            // 使用新的UserBatchRecordService实现统计概览
            var allRecords = await _userBatchRecordService.GetRecordsByDateRangeAsync(start, end, currentUserInfo);

            var newOverview = new
            {
                TotalViewCount = allRecords.Count,
                TotalCompleteViewCount = allRecords.Count(r => r.IsCompleted),
                AvgCompleteRate = allRecords.Count > 0 ? (decimal)allRecords.Count(r => r.IsCompleted) / allRecords.Count * 100 : 0m,
                TotalCorrectAnswerCount = allRecords.Sum(r => r.CorrectAnswers),
                TotalAnswerCount = allRecords.Sum(r => r.TotalQuestions),
                AvgCorrectRate = allRecords.Sum(r => r.TotalQuestions) > 0 ? (decimal)allRecords.Sum(r => r.CorrectAnswers) / allRecords.Sum(r => r.TotalQuestions) * 100 : 0m,
                TotalRewardCount = allRecords.Count(r => r.RewardAmount > 0),
                TotalRewardAmount = allRecords.Sum(r => r.RewardAmount),
                TotalRewardAmountYuan = allRecords.Sum(r => r.RewardAmount)
            };

            // 转换为旧格式以保持兼容性
            return new StatisticsSummaryDto
            {
                TotalViewCount = newOverview.TotalViewCount,
                TotalCompleteViewCount = newOverview.TotalCompleteViewCount,
                AverageCompleteRate = newOverview.AvgCompleteRate,
                TotalNewUserCount = 0, // 新统计暂不支持新用户统计
                TotalCorrectAnswerCount = newOverview.TotalCorrectAnswerCount,
                TotalAnswerCount = newOverview.TotalAnswerCount,
                AverageCorrectRate = newOverview.AvgCorrectRate,
                TotalRewardCount = newOverview.TotalRewardCount,
                TotalRewardAmount = (int)(newOverview.TotalRewardAmountYuan * 100) // 转换为分
            };
        }

        /// <summary>
        /// 获取概览统计数据（重定向到实时统计服务，带权限验证）
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>概览统计数据</returns>
        public async Task<OverviewStatisticsDto> GetOverviewStatisticsAsync(DateTime? startDate, DateTime? endDate, CurrentUserInfoDto currentUserInfo)
        {
            // 权限验证：转换CurrentUserInfoDto为UserInfo进行验证
            var userInfo = new UserInfo
            {
                UserId = currentUserInfo.UserId,
                UserName = currentUserInfo.UserName,
                UserType = currentUserInfo.UserType
            };
            ValidateUserTypePermission(userInfo, [1, 2, 3], "权限不足，无法查看概览统计数据");

            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;

            // 使用新的UserBatchRecordService实现统计概览
            var allRecords = await _userBatchRecordService.GetRecordsByDateRangeAsync(start, end, currentUserInfo);

            var newOverview = new
            {
                TotalUsers = allRecords.Select(r => r.UserId).Distinct().Count(),
                TotalViewCount = allRecords.Count,
                TotalCompleteViewCount = allRecords.Count(r => r.IsCompleted),
                AvgCompleteRate = allRecords.Count > 0 ? (decimal)allRecords.Count(r => r.IsCompleted) / allRecords.Count * 100 : 0m,
                TotalRewardCount = allRecords.Count(r => r.RewardAmount > 0),
                TotalRewardAmount = allRecords.Sum(r => r.RewardAmount),
                TotalRewardAmountYuan = allRecords.Sum(r => r.RewardAmount),
                AvgCorrectRate = allRecords.Sum(r => r.TotalQuestions) > 0 ? (decimal)allRecords.Sum(r => r.CorrectAnswers) / allRecords.Sum(r => r.TotalQuestions) * 100 : 0m
            };

            // 转换为旧格式
            return new OverviewStatisticsDto
            {
                TotalUsers = newOverview.TotalUsers,
                NewUsers = 0, // 新统计暂不支持新用户统计
                TotalViews = newOverview.TotalViewCount,
                CompleteViews = newOverview.TotalCompleteViewCount,
                CompleteRate = newOverview.AvgCompleteRate,
                TotalRewards = newOverview.TotalRewardCount,
                TotalRewardAmount = newOverview.TotalRewardAmountYuan,
                AvgCorrectRate = newOverview.AvgCorrectRate
            };
        }

        /// <summary>
        /// 获取用户统计数据（重定向到实时统计服务，带权限验证）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="statType">统计类型（已废弃）</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>统计记录列表</returns>
        public async Task<List<StatisticsResponseDto>> GetUserStatisticsAsync(string userId, string? statType, DateTime? startDate, DateTime? endDate, CurrentUserInfoDto currentUserInfo)
        {
            // 权限验证：转换CurrentUserInfoDto为UserInfo进行验证
            var userInfo = new UserInfo
            {
                UserId = currentUserInfo.UserId,
                UserName = currentUserInfo.UserName,
                UserType = currentUserInfo.UserType
            };
            ValidateUserTypePermission(userInfo, [1, 2, 3], "权限不足，无法查看用户统计数据");

            // 额外权限检查：员工只能查看自己的数据
            if (userInfo.UserType == 3 && userId != userInfo.UserId)
            {
                throw new BusinessException("员工只能查看自己的统计数据");
            }

            // 管理员权限检查：只能查看员工的数据
            if (userInfo.UserType == 2)
            {
                var accessibleUserIds = await GetAccessibleUserIdsAsync(userInfo);
                if (accessibleUserIds != null && !accessibleUserIds.Contains(userId))
                {
                    throw new BusinessException("管理员只能查看员工用户的统计数据");
                }
            }

            // 使用新的UserBatchRecordService实现用户统计
            var userRecords = await _userBatchRecordService.GetUserRecordsAsync(userId);

            // 按日期过滤
            if (startDate.HasValue || endDate.HasValue)
            {
                var start = startDate ?? DateTime.MinValue;
                var end = endDate ?? DateTime.MaxValue;
                userRecords = userRecords.Where(r => r.CreateTime >= start && r.CreateTime <= end).ToList();
            }

            // 转换为旧格式
            return userRecords.Select(record => new StatisticsResponseDto
            {
                Id = record.Id,
                StatDate = record.CreateTime.Date,
                BatchId = record.BatchId,
                UserId = record.UserId,
                ViewCount = 1, // 每个记录代表一次观看
                CompleteViewCount = record.IsCompleted ? 1 : 0,
                CorrectAnswerCount = record.CorrectAnswers,
                TotalAnswerCount = record.TotalQuestions,
                CorrectRate = record.TotalQuestions > 0 ? (decimal)record.CorrectAnswers / record.TotalQuestions * 100 : 0,
                RewardAmount = (int)record.RewardAmount,
                CreateTime = record.CreateTime,
                UpdateTime = record.CreateTime
            }).ToList();
        }

        /// <summary>
        /// 将新的用户每日统计DTO转换为旧的统计响应DTO格式
        /// </summary>
        /// <param name="userDailyStats">用户每日统计DTO</param>
        /// <returns>旧格式的统计响应DTO</returns>
        private static StatisticsResponseDto ConvertToLegacyFormat(UserDailyStatisticsDto userDailyStats)
        {
            return new StatisticsResponseDto
            {
                Id = userDailyStats.Id,
                StatDate = userDailyStats.StatDate,
                StatType = "daily", // 固定为daily类型
                UserId = userDailyStats.UserId,
                VideoId = null, // 新统计不按视频维度
                BatchId = 0, // 新统计不按批次维度，使用0表示无批次
                AdminId = null,
                EmployeeId = userDailyStats.EmployeeId,
                ViewCount = userDailyStats.ViewCount,
                CompleteViewCount = userDailyStats.CompleteViewCount,
                ShareCount = userDailyStats.ShareCount,
                LikeCount = userDailyStats.LikeCount,
                CommentCount = userDailyStats.CommentCount,
                DownloadCount = userDailyStats.DownloadCount,
                Duration = userDailyStats.TotalViewDuration,
                TotalAnswerCount = userDailyStats.AnswerCount,
                CorrectAnswerCount = userDailyStats.CorrectAnswerCount,
                RewardCount = userDailyStats.RewardCount,
                RewardAmount = (int)(userDailyStats.RewardAmountYuan * 100), // 转换为分
                NewUserCount = 0, // 新统计暂不支持
                CreateTime = userDailyStats.CreateTime,
                UpdateTime = userDailyStats.UpdateTime
            };
        }
    }
}
