<template>
  <view class="permission-test">
    <view class="header">
      <text class="title">权限调试页面</text>
    </view>
    
    <view class="section">
      <text class="section-title">用户信息</text>
      <view class="info-item">
        <text class="label">用户名:</text>
        <text class="value">{{ currentUser.userName || '未知' }}</text>
      </view>
      <view class="info-item">
        <text class="label">用户类型:</text>
        <text class="value">{{ currentUser.userType || '未知' }}</text>
      </view>
      <view class="info-item">
        <text class="label">权限级别:</text>
        <text class="value">{{ currentPermissionLevel }}</text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">功能权限测试</text>
      <view class="permission-item" v-for="feature in testFeatures" :key="feature.key">
        <text class="feature-name">{{ feature.name }}</text>
        <text class="permission-result" :class="{ 'allowed': canUseFeature(feature.key), 'denied': !canUseFeature(feature.key) }">
          {{ canUseFeature(feature.key) ? '✅ 允许' : '❌ 拒绝' }}
        </text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">页面权限测试</text>
      <view class="permission-item" v-for="page in testPages" :key="page.path">
        <text class="feature-name">{{ page.name }}</text>
        <text class="permission-result" :class="{ 'allowed': canAccessPage(page.path), 'denied': !canAccessPage(page.path) }">
          {{ canAccessPage(page.path) ? '✅ 允许' : '❌ 拒绝' }}
        </text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">角色权限测试</text>
      <view class="permission-item" v-for="role in testRoles" :key="role">
        <text class="feature-name">{{ role }}</text>
        <text class="permission-result" :class="{ 'allowed': hasRole(role), 'denied': !hasRole(role) }">
          {{ hasRole(role) ? '✅ 拥有' : '❌ 没有' }}
        </text>
      </view>
    </view>
    
    <view class="actions">
      <button @click="refreshUserInfo" class="refresh-btn">刷新用户信息</button>
      <button @click="goBack" class="back-btn">返回</button>
    </view>
  </view>
</template>

<script>
import permissionMixin from '@/mixins/permission-mixin.js'
import { getUserDisplayInfo, getCompleteUserDisplayInfo } from '@/utils/user-info-manager.js'

export default {
  mixins: [permissionMixin],
  
  data() {
    return {
      currentUser: {},
      testFeatures: [
        { key: 'view_dashboard', name: '查看仪表板' },
        { key: 'manage_users', name: '管理用户' },
        { key: 'user_audit', name: '用户审核' },
        { key: 'create_user', name: '创建用户' },
        { key: 'edit_user', name: '编辑用户' },
        { key: 'delete_user', name: '删除用户' },
        { key: 'upload_video', name: '上传视频' },
        { key: 'manage_video', name: '管理视频' }
      ],
      testPages: [
        { path: '/pages/index/index', name: '首页' },
        { path: '/pages/admin/users/user-management', name: '用户管理' },
        { path: '/pages/admin/media/index', name: '媒体管理' },
        { path: '/pages/admin/media/upload', name: '视频上传' }
      ],
      testRoles: ['admin', 'super_admin', 'manager', 'employee', 'user']
    }
  },
  
  computed: {
    currentPermissionLevel() {
      return this.getPermissionLevel(this.currentUser.userType)
    }
  },
  
  onLoad() {
    this.loadUserInfo()
  },
  
  methods: {
    async loadUserInfo() {
      try {
        this.currentUser = getUserDisplayInfo()
        console.log('权限测试页面 - 用户信息:', this.currentUser)
        
        const completeInfo = await getCompleteUserDisplayInfo(false)
        if (completeInfo) {
          this.currentUser = completeInfo
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        uni.showToast({
          title: '加载用户信息失败',
          icon: 'none'
        })
      }
    },
    
    async refreshUserInfo() {
      uni.showLoading({ title: '刷新中...' })
      await this.loadUserInfo()
      uni.hideLoading()
      uni.showToast({
        title: '刷新完成',
        icon: 'success'
      })
    },
    
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.permission-test {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.info-item, .permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child, .permission-item:last-child {
  border-bottom: none;
}

.label, .feature-name {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.permission-result {
  font-size: 28rpx;
  font-weight: bold;
}

.permission-result.allowed {
  color: #4CAF50;
}

.permission-result.denied {
  color: #F44336;
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.refresh-btn, .back-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.refresh-btn {
  background-color: #007AFF;
  color: white;
}

.back-btn {
  background-color: #666;
  color: white;
}
</style>
