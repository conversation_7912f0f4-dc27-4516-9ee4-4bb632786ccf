using BLL.Common;
using Common.Autofac;
using Common.JWT;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Dto.VideoDto;

namespace BLL.VideoService
{
    /// <summary>
    /// 标签统计服务（简化版）
    /// 暂时只返回"未指定标签"统计，满足当前仪表板需求
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class TagStatisticsService : BasePermissionService
    {
        public TagStatisticsService(
            UserDAL userDAL,
            SysUserDAL sysUserDAL) : base(userDAL, sysUserDAL)
        {
        }

        /// <summary>
        /// 获取标签统计信息（带权限过滤）
        /// 当前简化实现：所有用户都归类为"未指定标签"
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>标签统计列表</returns>
        public async Task<List<TagStatisticsDto>> GetTagStatisticsAsync(UserInfo? currentUserInfo = null)
        {
            int totalUsers;

            if (currentUserInfo != null)
            {
                // 验证权限
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看标签统计");

                // 根据用户权限获取用户数量
                var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);
                if (accessibleUserIds == null)
                {
                    // 超级管理员，可以查看所有用户
                    totalUsers = await _userDAL.GetCountAsync();
                }
                else
                {
                    // 管理员或员工，只能查看权限范围内的用户
                    totalUsers = accessibleUserIds.Count;
                }
            }
            else
            {
                // 未提供用户信息时，返回所有用户数量（向后兼容）
                totalUsers = await _userDAL.GetCountAsync();
            }

            // 返回简化的标签统计：所有用户都是"未指定标签"
            return
            [
                new TagStatisticsDto
                {
                    TagId = null,
                    TagName = "未指定标签",
                    UserCount = totalUsers,
                    IsUntagged = true,
                    Percentage = 100.0m
                }
            ];
        }

        /// <summary>
        /// 获取未指定标签的用户数量
        /// </summary>
        /// <returns>未指定标签的用户数量</returns>
        public async Task<int> GetUntaggedUserCountAsync()
        {
            // 当前所有用户都是未指定标签
            return await _userDAL.GetCountAsync();
        }
    }
}
